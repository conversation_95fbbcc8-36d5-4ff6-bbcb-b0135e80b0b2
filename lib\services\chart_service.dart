import '../services/api_service.dart';
import '../utils/toast_utils.dart';
import '../constants/url_form.dart';
// import '../services/mock_data_service.dart';

/// 图表服务API，从数据库中获取各种图表数据
class ChartService {
  final ApiService _apiService;

  ChartService(this._apiService);

  /// 获取宠物活动状态数据
  Future<Map<String, dynamic>> fetchActivityChartData(
      String deviceName, String mode, DateTime datetime) async {
    final body = {
      'device_name': deviceName,
      'mode': mode, // 按天查询，可以是 day、week、month模式
      'datetime': datetime.toIso8601String().split('T').first,
    };
    final jsonResponse = await _apiService.postRequest(
      UrlFormat.SensorActivityChart,
      body,
    );
    final data = jsonResponse['data'];
    final code = jsonResponse['code'];
    if (code == 200) {
      return data;
    } else {
      ToastUtils.showError('获取宠物活动状态统计数据失败');
      throw Exception('获取宠物活动状态统计数据失败');
    }

    //测试阶段使用mock数据
    // final jsonResponse = MockDataService.generateActivityChartData(body);
    // final data = jsonResponse['data'];
    // final code = jsonResponse['code'];
    // if (code == 200) {
    //   return data;
    // } else {
    //   ToastUtils.showError('获取宠物活动状态统计数据失败');
    //   throw Exception('获取宠物活动状态统计数据失败');
    // }
  }

  Future<Map<String, dynamic>> fetchStepCountChartData(
      String deviceName, String mode, DateTime datetime) async {
    final body = {
      'device_name': deviceName,
      'mode': mode, // 按天查询，可以是 day、week、month模式
      'date': datetime.toIso8601String().split('T').first,
    };
    final jsonResponse = await _apiService.postRequest(
      UrlFormat.SensorStepsChart,
      body,
    );
    final data = jsonResponse['data'];
    final code = jsonResponse['code'];
    if (code == 200) {
      return data;
    } else {
      ToastUtils.showError('获取宠物步数统计数据失败');
      throw Exception('获取宠物步数统计数据失败');
    }

    //测试阶段使用mock数据
    // final jsonResponse = MockDataService.generateStepCountChartData(body);
    // final data = jsonResponse['data'];
    // final code = jsonResponse['code'];
    // if (code == 200) {
    //   return data;
    // } else {
    //   ToastUtils.showError('获取宠物步数统计数据失败');
    //   throw Exception('获取宠物步数统计数据失败');
    // }
  }

  Future<Map<String, dynamic>> fetchCalorieChartData(
      String deviceName, String mode, DateTime datetime) async {
    final body = {
      'device_name': deviceName,
      'mode': mode, // 按天查询，可以是 day、week、month模式
      'date': datetime.toIso8601String().split('T').first,
    };
    final jsonResponse = await _apiService.postRequest(
      UrlFormat.SensorKcalChart,
      body,
    );
    final data = jsonResponse['data'];
    final code = jsonResponse['code'];
    if (code == 200) {
      return data;
    } else {
      ToastUtils.showError('获取宠物卡路里统计数据失败');
      throw Exception('获取宠物卡路里统计数据失败');
    }

    //测试阶段使用mock数据
    // final jsonResponse = MockDataService.generateCalorieChartData(body);
    // final data = jsonResponse['data'];
    // final code = jsonResponse['code'];
    // if (code == 200) {
    //   return data;
    // } else {
    //   ToastUtils.showError('获取宠物卡路里统计数据失败');
    //   throw Exception('获取宠物卡路里统计数据失败');
    // }
  }

  Future<Map<String, dynamic>> fetchTemperatureChartData(
      String deviceName, String mode, DateTime datetime) async {
    final body = {
      'device_name': deviceName,
      'mode': mode, // 按天查询，可以是 day、week、month模式
      'date': datetime.toIso8601String().split('T').first,
    };
    final jsonResponse = await _apiService.postRequest(
      UrlFormat.SensorTemperatureChart,
      body,
    );
    final data = jsonResponse['data'];
    final code = jsonResponse['code'];
    if (code == 200) {
      return data;
    } else {
      ToastUtils.showError('获取宠物卡路里统计数据失败');
      throw Exception('获取宠物卡路里统计数据失败');
    }

    //测试阶段使用mock数据
    // final jsonResponse = MockDataService.generateTemperatureChartData(body);
    // final data = jsonResponse['data'];
    // final code = jsonResponse['code'];
    // if (code == 200) {
    //   return data;
    // } else {
    //   ToastUtils.showError('获取宠物体温统计数据失败');
    //   throw Exception('获取宠物体温统计数据失败');
    // }
  }

  Future<Map<String, dynamic>> fetchEmotionStatusChartData(
      String deviceName, String mode, DateTime datetime) async {
    final body = {
      'device_name': deviceName,
      'mode': mode, // 按天查询，可以是 day、week、month模式
      'date': datetime.toIso8601String().split('T').first,
    };
    final jsonResponse = await _apiService.postRequest(
      UrlFormat.SensorEmotionChart,
      body,
    );
    final data = jsonResponse['data'];
    final code = jsonResponse['code'];
    if (code == 200) {
      return data;
    } else {
      ToastUtils.showError('获取宠物卡路里统计数据失败');
      throw Exception('获取宠物卡路里统计数据失败');
    }

    //测试阶段使用mock数据
    // final jsonResponse = MockDataService.generateEmotionStatusChartData(body);
    // final data = jsonResponse['data'];
    // final code = jsonResponse['code'];
    // if (code == 200) {
    //   return data;
    // } else {
    //   ToastUtils.showError('获取宠物情绪统计数据失败');
    //   throw Exception('获取宠物情绪统计数据失败');
    // }
  }

  Future<Map<String, dynamic>> fetchHealthDetectionChartData(
      String deviceName, String mode, DateTime datetime) async {
    final body = {
      'device_name': deviceName,
      'mode': mode, // 按天查询，可以是 day、week、month模式
      'date': datetime.toIso8601String().split('T').first,
    };
    final jsonResponse = await _apiService.postRequest(
      UrlFormat.SensorHealthChart,
      body,
    );
    final data = jsonResponse['data'];
    final code = jsonResponse['code'];
    if (code == 200) {
      return data;
    } else {
      ToastUtils.showError('获取宠物卡路里统计数据失败');
      throw Exception('获取宠物卡路里统计数据失败');
    }

    //测试阶段使用mock数据
    // final jsonResponse = MockDataService.generateHealthDetectionChartData(body);
    // final data = jsonResponse['data'];
    // final code = jsonResponse['code'];
    // if (code == 200) {
    //   return data;
    // } else {
    //   ToastUtils.showError('获取宠物健康检测统计数据失败');
    //   throw Exception('获取宠物健康检测统计数据失败');
    // }
  }
}
